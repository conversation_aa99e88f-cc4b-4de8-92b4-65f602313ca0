/**
 * 测试Dialog功能是否正常工作
 */

import { showConfirmation } from '~/compositions/useConfirmation'

export async function testDialogFunctionality(): Promise<boolean> {
  try {
    console.log('开始测试Dialog功能...')
    
    // 测试基本确认对话框
    const result = await showConfirmation({
      title: '测试对话框',
      message: '这是一个测试对话框，请点击确认或取消',
      confirmText: '确认',
      cancelText: '取消',
      type: 'info'
    })
    
    console.log('Dialog测试结果:', result)
    console.log('Dialog功能测试完成')
    
    return true
  } catch (error) {
    console.error('Dialog功能测试失败:', error)
    return false
  }
}

// 在开发环境下提供手动测试函数
if (import.meta.env.DEV) {
  // 将测试函数暴露到全局，方便在控制台调用
  (window as any).testDialog = testDialogFunctionality
  console.log('开发环境：可以在控制台调用 testDialog() 来测试Dialog功能')
}
