import { GM_cookie, GM_xmlhttpRequest } from '$'

interface Response {
  data: any
  code: number
  msg: string
}

export class GMRequest {
  getByToken(url: string, token: string, preserveMainCookie = false) {
    return new Promise<Response>((resolve, reject) => {
      GM_xmlhttpRequest({
        method: 'GET',
        url,
        cookie: `m_gray_switch=1; m_gray_switch_=1; ts_session_id=${token}; ts_session_id_=${token}`,
        headers: {
          'Content-Type': 'application/json',
        },
        onload(response) {
          // 只清理灰度相关的cookie，保留主要的登录cookie
          GM_cookie.delete({ name: 'm_gray_token', url: '.seasunwbl.com' })
          GM_cookie.delete({ name: 'm_gray_token_', url: '.seasunwbl.com' })

          // 如果不需要保留主cookie，则清理所有cookie（用于多开账号请求）
          if (!preserveMainCookie) {
            GM_cookie.delete({ name: 'm_gray_switch', url: '.seasunwbl.com' })
            GM_cookie.delete({ name: 'm_gray_switch_', url: '.seasunwbl.com' })
            GM_cookie.delete({ name: 'ts_session_id', url: '.seasunwbl.com' })
            GM_cookie.delete({ name: 'ts_session_id_', url: '.seasunwbl.com' })
          }

          resolve(JSON.parse(response.responseText))
        },
        onerror(error) {
          reject(error)
        },
      })
    })
  }

  // 专门用于账号切换的安全请求方法，不会清理主要的登录cookie
  getByTokenSafe(url: string, token: string) {
    return this.getByToken(url, token, true)
  }
}
