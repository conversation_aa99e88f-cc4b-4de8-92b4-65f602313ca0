import { nextTick, ref } from 'vue'
import { setCurrRole } from '~/compositions/useCurrRole'
import { accountTokenList, currAccount, setToken } from '~/compositions/useMultiOpen'
import { getLocalStorage, getSessionStorage, setSessionStorage } from '~/utils/utils'
import { errNotify, successNotify } from '~/compositions/useNotify'

// 账号切换状态
const isSwitching = ref(false)
const switchProgress = ref('')

/**
 * 安全的账号切换函数，不会导致登录状态丢失
 * @param token 要切换到的账号token，如果为空则切换到主账号
 */
export async function switchAccountSafely(token?: string) {
  if (isSwitching.value) {
    errNotify('账号切换正在进行中，请稍候')
    return false
  }

  try {
    isSwitching.value = true
    switchProgress.value = '正在切换账号...'

    // 检查是否真的需要切换
    const currentToken = getSessionStorage('currToken')
    const targetToken = token || getLocalStorage('token')

    if (currentToken === targetToken) {
      errNotify('已经是当前账号了')
      return false
    }

    switchProgress.value = '正在验证账号信息...'

    // 使用安全的setCurrRole函数，不会清理主要登录cookie
    await setCurrRole(targetToken, true)

    switchProgress.value = '正在更新界面状态...'

    // 更新当前账号显示
    await updateAccountDisplay(targetToken)

    switchProgress.value = '账号切换完成'
    successNotify('账号切换成功')

    return true
  }
  catch (error) {
    console.error('账号切换失败:', error)
    errNotify('账号切换失败，请重试')
    return false
  }
  finally {
    isSwitching.value = false
    switchProgress.value = ''
  }
}

/**
 * 更新账号显示信息
 */
async function updateAccountDisplay(token: string) {
  // 更新当前账号显示
  const baseInfo = getSessionStorage('baseInfo')
  if (baseInfo?.account) {
    currAccount.value = `账号：${baseInfo.account}`
    setSessionStorage('account', baseInfo.account)
  }

  // 触发界面更新
  await nextTick()

  // 更新相关的响应式状态
  await updateReactiveStates(baseInfo)

  // 触发自定义事件，通知其他组件账号已切换
  window.dispatchEvent(new CustomEvent('accountSwitched', {
    detail: { token, baseInfo },
  }))
}

/**
 * 更新响应式状态，避免页面刷新
 */
async function updateReactiveStates(baseInfo: any) {
  try {
    // 更新当前token
    if (baseInfo?.ts_session_id)
      setSessionStorage('currToken', baseInfo.ts_session_id)

    // 等待状态更新完成
    await nextTick()

    console.log('响应式状态更新完成，无需页面刷新')
  }
  catch (error) {
    console.error('更新响应式状态失败:', error)
    throw error
  }
}

/**
 * 检查账号是否处于活跃状态
 */
function isAccountActive(token: string): boolean {
  const currentToken = getSessionStorage('currToken')
  return token === currentToken
}

/**
 * 获取账号信息
 */
function getAccountInfo(token: string) {
  if (!token)
    return null

  // 检查是否是主账号
  const mainToken = getLocalStorage('token')
  if (token === mainToken) {
    return {
      account: getSessionStorage('account', '主账号'),
      isMain: true,
      isActive: isAccountActive(token),
    }
  }

  // 检查多开账号列表
  const account = accountTokenList.value.find(item => item.token === token)
  if (account) {
    return {
      account: account.account,
      isMain: false,
      isActive: isAccountActive(token),
      roleInfo: account.roleInfo,
      isSelectedRole: account.isSelectedRole,
      expire: account.expire,
    }
  }

  return null
}

/**
 * 获取所有可用账号列表
 */
function getAllAccounts() {
  const accounts = []

  // 添加主账号
  const mainToken = getLocalStorage('token')
  if (mainToken) {
    accounts.push({
      token: mainToken,
      ...getAccountInfo(mainToken),
    })
  }

  // 添加多开账号
  accountTokenList.value.forEach((account) => {
    accounts.push({
      token: account.token,
      ...getAccountInfo(account.token),
    })
  })

  return accounts
}

export function useAccountSwitch() {
  return {
    isSwitching,
    switchProgress,
    switchAccountSafely,
    isAccountActive,
    getAccountInfo,
    getAllAccounts,
  }
}
