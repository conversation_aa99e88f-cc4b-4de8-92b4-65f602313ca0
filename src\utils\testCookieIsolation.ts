/**
 * Cookie隔离机制测试工具
 * 用于验证多开抢号过程中Cookie隔离是否正常工作
 */

import { cookieIsolationManager } from './CookieIsolationManager'
import { getSessionStorage, setSessionStorage } from './utils'

interface TestResult {
  testName: string
  passed: boolean
  message: string
  details?: any
}

class CookieIsolationTester {
  private results: TestResult[] = []

  /**
   * 运行所有测试
   */
  async runAllTests(): Promise<TestResult[]> {
    this.results = []
    
    console.log('开始Cookie隔离机制测试...')
    
    await this.testBasicIsolation()
    await this.testCookieBackupRestore()
    await this.testMultipleIsolationCalls()
    await this.testTimeoutHandling()
    await this.testStateManagement()
    
    console.log('Cookie隔离机制测试完成')
    this.printResults()
    
    return this.results
  }

  /**
   * 测试基本隔离功能
   */
  private async testBasicIsolation(): Promise<void> {
    try {
      // 初始状态检查
      const initialState = cookieIsolationManager.isInIsolationMode()
      if (initialState) {
        cookieIsolationManager.forceEndIsolation()
      }

      // 启动隔离
      cookieIsolationManager.startIsolation()
      const isIsolated = cookieIsolationManager.isInIsolationMode()
      
      if (!isIsolated) {
        this.addResult('基本隔离功能', false, '隔离模式启动失败')
        return
      }

      // 结束隔离
      cookieIsolationManager.endIsolation()
      const isEnded = !cookieIsolationManager.isInIsolationMode()

      this.addResult('基本隔离功能', isEnded, isEnded ? '隔离模式正常启动和结束' : '隔离模式结束失败')
    } catch (error) {
      this.addResult('基本隔离功能', false, `测试异常: ${error.message}`)
    }
  }

  /**
   * 测试Cookie备份和恢复
   */
  private async testCookieBackupRestore(): Promise<void> {
    try {
      // 设置测试用的session数据
      const testToken = 'test_token_12345'
      setSessionStorage('currToken', testToken)

      // 启动隔离（应该备份当前状态）
      cookieIsolationManager.startIsolation()
      
      // 检查是否有备份
      const backup = getSessionStorage('mainAccountCookieBackup')
      const hasBackup = backup && backup.ts_session_id

      // 结束隔离（应该恢复状态）
      cookieIsolationManager.endIsolation()
      
      // 检查备份是否被清理
      const backupAfterRestore = getSessionStorage('mainAccountCookieBackup')
      const backupCleared = !backupAfterRestore

      const passed = hasBackup && backupCleared
      this.addResult('Cookie备份恢复', passed, passed ? 'Cookie备份和恢复正常' : '备份或恢复失败', {
        hasBackup,
        backupCleared,
        backup
      })
    } catch (error) {
      this.addResult('Cookie备份恢复', false, `测试异常: ${error.message}`)
    }
  }

  /**
   * 测试多次隔离调用
   */
  private async testMultipleIsolationCalls(): Promise<void> {
    try {
      // 确保初始状态
      cookieIsolationManager.forceEndIsolation()

      // 多次启动隔离
      cookieIsolationManager.startIsolation()
      cookieIsolationManager.startIsolation() // 应该被忽略
      cookieIsolationManager.startIsolation() // 应该被忽略

      const isIsolated = cookieIsolationManager.isInIsolationMode()

      // 多次结束隔离
      cookieIsolationManager.endIsolation()
      cookieIsolationManager.endIsolation() // 应该被忽略

      const isEnded = !cookieIsolationManager.isInIsolationMode()

      const passed = isIsolated && isEnded
      this.addResult('多次隔离调用', passed, passed ? '多次调用处理正常' : '多次调用处理异常')
    } catch (error) {
      this.addResult('多次隔离调用', false, `测试异常: ${error.message}`)
    }
  }

  /**
   * 测试超时处理
   */
  private async testTimeoutHandling(): Promise<void> {
    try {
      // 启动隔离
      cookieIsolationManager.startIsolation()
      
      // 模拟超时（修改内部状态）
      const state = cookieIsolationManager.getIsolationState()
      // 这里我们无法直接修改私有状态，所以只能测试检查方法
      
      // 检查超时检查方法是否存在
      cookieIsolationManager.checkIsolationTimeout()
      
      // 正常结束隔离
      cookieIsolationManager.endIsolation()

      this.addResult('超时处理', true, '超时检查方法正常执行')
    } catch (error) {
      this.addResult('超时处理', false, `测试异常: ${error.message}`)
    }
  }

  /**
   * 测试状态管理
   */
  private async testStateManagement(): Promise<void> {
    try {
      // 确保初始状态
      cookieIsolationManager.forceEndIsolation()

      // 获取初始状态
      const initialState = cookieIsolationManager.getIsolationState()
      
      // 启动隔离
      cookieIsolationManager.startIsolation()
      const isolatedState = cookieIsolationManager.getIsolationState()
      
      // 设置token
      cookieIsolationManager.setCurrentIsolatedToken('test_token')
      const stateWithToken = cookieIsolationManager.getIsolationState()
      
      // 结束隔离
      cookieIsolationManager.endIsolation()
      const endState = cookieIsolationManager.getIsolationState()

      const passed = !initialState.isIsolated && 
                    isolatedState.isIsolated && 
                    stateWithToken.currentIsolatedToken === 'test_token' &&
                    !endState.isIsolated

      this.addResult('状态管理', passed, passed ? '状态管理正常' : '状态管理异常', {
        initialState,
        isolatedState,
        stateWithToken,
        endState
      })
    } catch (error) {
      this.addResult('状态管理', false, `测试异常: ${error.message}`)
    }
  }

  /**
   * 添加测试结果
   */
  private addResult(testName: string, passed: boolean, message: string, details?: any): void {
    this.results.push({
      testName,
      passed,
      message,
      details
    })
  }

  /**
   * 打印测试结果
   */
  private printResults(): void {
    console.log('\n=== Cookie隔离机制测试结果 ===')
    
    let passedCount = 0
    let totalCount = this.results.length

    this.results.forEach((result, index) => {
      const status = result.passed ? '✅ PASS' : '❌ FAIL'
      console.log(`${index + 1}. ${result.testName}: ${status}`)
      console.log(`   ${result.message}`)
      
      if (result.details) {
        console.log(`   详细信息:`, result.details)
      }
      
      if (result.passed) {
        passedCount++
      }
      
      console.log('')
    })

    console.log(`总计: ${passedCount}/${totalCount} 测试通过`)
    
    if (passedCount === totalCount) {
      console.log('🎉 所有测试通过！Cookie隔离机制工作正常。')
    } else {
      console.log('⚠️  部分测试失败，请检查Cookie隔离机制实现。')
    }
  }

  /**
   * 获取测试摘要
   */
  getTestSummary(): { passed: number, total: number, success: boolean } {
    const passed = this.results.filter(r => r.passed).length
    const total = this.results.length
    return {
      passed,
      total,
      success: passed === total
    }
  }
}

// 导出测试器实例
export const cookieIsolationTester = new CookieIsolationTester()

// 导出便捷测试函数
export async function testCookieIsolation(): Promise<boolean> {
  const results = await cookieIsolationTester.runAllTests()
  const summary = cookieIsolationTester.getTestSummary()
  return summary.success
}

// 在开发环境下自动运行测试
if (import.meta.env.DEV) {
  // 延迟执行，确保所有模块都已加载
  setTimeout(() => {
    console.log('开发环境检测到，自动运行Cookie隔离测试...')
    testCookieIsolation()
  }, 2000)
}
