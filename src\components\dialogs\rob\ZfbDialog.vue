<script setup lang="ts">
import { useZfbDialog } from '~/compositions/useZfb'
import CheckItem from '~/components/CheckItem.vue'
import CardHeader from '~/components/CardHeader.vue'
import DeliveryAddressSelector from '~/components/DeliveryAddressSelector.vue'
import type { AdditionalServiceOptions } from '~/type'
import { isRoleMultiOpen, isSkinMultiOpen } from '~/compositions/useMultiOpen'
import MultiQrcode from '~/components/MultiQrcode.vue'

const {
  transferServiceObj,
  currentDetail,
  additionalService,
  currentStep,
  isShowZfbDialog,
  currentRow,
  closeZfbDialog,
  qrcodeUrl,
  countdownText,
  additionalServiceSum,
  nextStep,
  onDeliveryInfoChanged,
} = useZfbDialog()
</script>

<template>
  <q-dialog v-model="isShowZfbDialog" persistent transition-show="scale" transition-hide="scale">
    <q-card flat bordered min-w-332px style="max-width: 1024px !important;">
      <template v-if="currentStep === 'selectAdditionalService'">
        <!--        选择附加服务 -->
        <CardHeader :title="currentRow?.type === 3 ? '请选择收货地址' : '请选择附加服务'" @close="closeZfbDialog()" />

        <!-- 外观商品收货地址选择 -->
        <q-card-section v-if="currentRow?.type === 3" class="q-pb-none">
          <DeliveryAddressSelector
            @delivery-info-changed="onDeliveryInfoChanged"
          />
          <q-separator class="q-mt-md" />
        </q-card-section>

        <q-card-section flex="~ col">
          <q-list>
            <template v-for="item in additionalService" :key="item.desc">
              <template v-if="item.name === 'transfer_service'">
                <q-expansion-item
                  v-model="item.selected" :disable="item.must_selected" dense hide-expand-icon
                  tag="label"
                >
                  <template #header>
                    <CheckItem
                      v-model="item.selected"
                      :price="(transferServiceObj?.value || 0) / 100" :title="item.desc"
                      :sub-info="`区服：${currentDetail!.zone_name} — ${currentDetail!.server_name}` "
                      :disabled="item.must_selected"
                    />
                  </template>
                  <q-card>
                    <q-card-section flex justify-right>
                      <q-select
                        v-model="transferServiceObj"
                        color="teal"
                        style="width: 80%" dense :options="item.option"
                        :option-label="(opt:AdditionalServiceOptions) => `${opt.zone_name} — ${opt.server_name}`"
                        :option-value="(opt:AdditionalServiceOptions) => `${opt.zone_id} — ${opt.server_name}`"
                        label="区服"
                      />
                    </q-card-section>
                  </q-card>
                </q-expansion-item>
              </template>
              <template v-else-if="item.name === 'change_group_service'">
                <q-item tag="label" :disable="item.must_selected">
                  <CheckItem
                    v-model="item.selected"
                    :price="item.value / 100" :title="item.desc"
                    :sub-info="`阵营：${currentDetail!.attrs.role_camp}`" :disabled="item.must_selected"
                  />
                </q-item>
              </template>
              <template v-else>
                <q-item tag="label" :disable="item.must_selected">
                  <CheckItem
                    v-model="item.selected"
                    font-12px
                    :price="item.value / 100" :title="item.desc"
                    :sub-info="item.must_selected ? '必选服务' : '非必选服务'" :disabled="item.must_selected"
                  />
                </q-item>
              </template>
            </template>
          </q-list>
        </q-card-section>
        <q-card-actions align="right">
          <!-- 只在角色商品购买时显示总价，外观商品不显示 -->
          <div v-if="currentRow?.type === 2" ml-4>
            共计：<span mx-1 text-red font="bold size-16px">{{ additionalServiceSum / 100 }}</span> 元
          </div>
          <q-space />
          <q-btn label="下一步" color="teal" flat @click="nextStep" />
        </q-card-actions>
      </template>
      <template v-else-if="currentStep === 'pay'">
        <CardHeader :title="countdownText || '请使用支付宝扫描二维码支付'" @close="closeZfbDialog()" />
        <q-card-section v-if="$q.screen.xs">
          请耐心等待公示期结束，公示期结束后会自动跳转支付宝支付
        </q-card-section>
        <q-card-section v-else class="flex justify-center">
          <template v-if="(isRoleMultiOpen && currentRow?.type === 2) || (isSkinMultiOpen && currentRow?.type === 3)">
            <MultiQrcode :qrcode-url="qrcodeUrl" />
          </template>
          <template v-else>
            <div id="qrcode">
              <q-img :src="qrcodeUrl" />
            </div>
          </template>
        </q-card-section>
        <q-card-section>
          <div class="q-mt-sm q-mb-xs flex items-center gap-2 font-size-20px">
            <q-img :src="currentRow!.goods_icon_url" width="50px" />
            <span>{{ `${currentRow!.role_sect} ${currentRow!.goods_name}` }}</span>
            <q-icon name="jx3_yuan" size="xs" />
            <span class="font-size-16px text-red">{{ currentRow!.goods_price / 100 }} 元</span>
          </div>
        </q-card-section>

        <q-card-section v-if="currentRow?.type === 2">
          <q-list dense>
            <template v-for="item in additionalService" :key="item.desc">
              <q-item v-if="item.selected">
                <q-item-section>
                  {{ item.desc }}
                </q-item-section>
                <q-item-section side class="font-size-24px text-red font-bold">
                  {{ item.name === 'transfer_service' ? transferServiceObj!.value / 100 : item.value / 100 }} 元
                </q-item-section>
              </q-item>
            </template>
            <q-item>
              <q-item-section>
                附加服务合计
              </q-item-section>
              <q-item-section side class="font-size-24px text-red font-bold">
                {{ additionalServiceSum / 100 }} 元
              </q-item-section>
            </q-item>
            <q-item>
              <q-item-section>
                总价
              </q-item-section>
              <q-item-section side class="font-size-28px text-red font-bold">
                {{ (currentRow!.goods_price + additionalServiceSum) / 100 }} 元
              </q-item-section>
            </q-item>
          </q-list>
        </q-card-section>
      </template>
    </q-card>
  </q-dialog>
</template>

<style scoped>
#qrcode {
  display: flex;
  width: 300px;
  height: 300px;
  background-color: #fff;
  box-shadow: 0 0 4px 4px #1688ff;
  position: relative;
  border-radius: 4px;
}

#qrcode:before {
  position: absolute;
  content: '支付宝二维码区域';
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
</style>
